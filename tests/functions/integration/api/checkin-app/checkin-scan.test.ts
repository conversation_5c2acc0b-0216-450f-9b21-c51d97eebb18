import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '../../../../../src/app/(payload)/api/checkin-app/checkin/scan/route'

// Mock dependencies
vi.mock('next/headers', () => ({
  headers: vi.fn().mockResolvedValue(new Headers()),
}))

vi.mock('../../../../../src/payload-config/getPayloadConfig', () => ({
  getPayload: vi.fn(),
}))

vi.mock('../../../../../src/utilities/handleNextErrorMsgResponse', () => ({
  handleNextErrorMsgResponse: vi.fn().mockImplementation((error) => {
    if (error instanceof Error) {
      const errorCode = error.message
      switch (errorCode) {
        case 'CHECKIN001':
          return Promise.resolve('Ticket not found')
        case 'CHECKIN005':
          return Promise.resolve('Unauthorized - Invalid admin user')
        case 'CHECKIN010':
          return Promise.resolve('Ticket code is required')
        case 'CHECKIN014':
          return Promise.resolve('Please choose event, it is required')
        case 'CHECKIN015':
          return Promise.resolve('Ticket is not paid or not valid for check-in')
        case 'CHECKIN016':
          return Promise.resolve('No order found for this ticket')
        case 'CHECKIN017':
          return Promise.resolve('Order is not completed/paid')
        case 'CHECKIN018':
          return Promise.resolve('Ticket has already been checked in')
        default:
          return Promise.resolve(error.message)
      }
    }
    return Promise.resolve('Unknown error')
  }),
}))

// Import mocked modules
import { getPayload } from '../../../../../src/payload-config/getPayloadConfig'
import { headers } from 'next/headers'
import { handleNextErrorMsgResponse } from '../../../../../src/utilities/handleNextErrorMsgResponse'

describe('Checkin Scan API', () => {
  const mockPayload = {
    auth: vi.fn(),
    db: {
      drizzle: {
        execute: vi.fn(),
      },
    },
  }

  const mockAdmin = {
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
  }

  beforeEach(() => {
    vi.resetAllMocks()
    vi.mocked(getPayload).mockResolvedValue(mockPayload as any)
    vi.mocked(headers).mockResolvedValue(new Headers())
  })

  describe('Authentication', () => {
    it('should return error when user is not authenticated', async () => {
      mockPayload.auth.mockResolvedValue({ user: null })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Unauthorized - Invalid admin user')
    })

    it('should proceed when user is authenticated', async () => {
      mockPayload.auth.mockResolvedValue({ user: mockAdmin })
      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.message).toBe('Ticket not found')
    })
  })

  describe('Input Validation', () => {
    beforeEach(() => {
      mockPayload.auth.mockResolvedValue({ user: mockAdmin })
    })

    it('should return error when ticketCode is missing', async () => {
      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Ticket code is required')
    })

    it('should return error when eventId is missing', async () => {
      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Please choose event, it is required')
    })
  })

  describe('Ticket Validation', () => {
    beforeEach(() => {
      mockPayload.auth.mockResolvedValue({ user: mockAdmin })
    })

    it('should return error when ticket is not found', async () => {
      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'NOTFOUND',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Ticket not found')
    })

    it('should return error when ticket is not booked', async () => {
      const mockTicket = {
        id: 1,
        ticket_code: 'ABC123',
        status: 'pending_payment',
        order_id: 1,
        order_status: 'processing',
        is_checked_in: false,
      }

      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [mockTicket] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Ticket is not paid or not valid for check-in')
    })

    it('should return error when order is not completed', async () => {
      const mockTicket = {
        id: 1,
        ticket_code: 'ABC123',
        status: 'booked',
        order_id: 1,
        order_status: 'processing',
        is_checked_in: false,
      }

      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [mockTicket] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Order is not completed/paid')
    })

    it('should return error when ticket is already checked in', async () => {
      const mockTicket = {
        id: 1,
        ticket_code: 'ABC123',
        status: 'booked',
        order_id: 1,
        order_status: 'completed',
        is_checked_in: true,
      }

      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [mockTicket] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.message).toBe('Ticket has already been checked in')
    })

    it('should return success when ticket is valid', async () => {
      const mockTicket = {
        id: 1,
        ticket_code: 'ABC123',
        attendee_name: 'John Doe',
        seat: 'A1',
        ticket_price_info: { name: 'VIP', price: 100000 },
        event_id: 1,
        event_schedule_id: '2024-01-15',
        status: 'booked',
        user_id: 1,
        email: '<EMAIL>',
        phone_number: '+84123456789',
        first_name: 'John',
        last_name: 'Doe',
        order_id: 1,
        order_status: 'completed',
        is_checked_in: false,
        check_in_time: null,
        checked_in_by_id: null,
        checked_in_by_email: null,
        ticket_given_time: null,
        ticket_given_by: null,
        event_title: 'Concert 2024',
      }

      mockPayload.db.drizzle.execute.mockResolvedValue({ rows: [mockTicket] })

      const request = new NextRequest('http://localhost/api/checkin-app/checkin/scan', {
        method: 'POST',
        body: JSON.stringify({
          ticketCode: 'ABC123',
          eventId: 1,
          eventScheduleId: '2024-01-15',
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Ticket is valid for check-in')
      expect(data.ticket).toMatchObject({
        id: 1,
        ticketCode: 'ABC123',
        attendeeName: 'John Doe',
        seat: 'A1',
        status: 'booked',
        isCheckedIn: false,
        user: {
          id: 1,
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
        },
        order: {
          id: 1,
          status: 'completed',
        },
      })
    })
  })
})
