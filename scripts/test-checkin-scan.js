#!/usr/bin/env node

/**
 * Test script for the enhanced checkin scan API
 * Usage: node scripts/test-checkin-scan.js
 */

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000'

async function testCheckinScanAPI() {
  console.log('🧪 Testing Enhanced Checkin Scan API\n')

  // Test cases
  const testCases = [
    {
      name: 'Valid ticket test',
      data: {
        ticketCode: 'ABC123DEF',
        eventId: 1,
        eventScheduleId: '2024-01-15'
      },
      expectedStatus: 200
    },
    {
      name: 'Missing ticket code',
      data: {
        eventId: 1,
        eventScheduleId: '2024-01-15'
      },
      expectedStatus: 400
    },
    {
      name: 'Missing event ID',
      data: {
        ticketCode: 'ABC123DEF',
        eventScheduleId: '2024-01-15'
      },
      expectedStatus: 400
    },
    {
      name: 'Non-existent ticket',
      data: {
        ticketCode: 'NOTFOUND123',
        eventId: 1,
        eventScheduleId: '2024-01-15'
      },
      expectedStatus: 400
    }
  ]

  for (const testCase of testCases) {
    console.log(`📋 Testing: ${testCase.name}`)
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/checkin-app/checkin/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Note: In real usage, you need to include Authorization header
          // 'Authorization': 'Bearer <admin-token>'
        },
        body: JSON.stringify(testCase.data)
      })

      const data = await response.json()
      
      console.log(`   Status: ${response.status} (expected: ${testCase.expectedStatus})`)
      console.log(`   Response: ${JSON.stringify(data, null, 2)}`)
      
      if (response.status === testCase.expectedStatus) {
        console.log('   ✅ Test passed\n')
      } else {
        console.log('   ❌ Test failed - unexpected status\n')
      }
      
    } catch (error) {
      console.log(`   ❌ Test failed with error: ${error.message}\n`)
    }
  }
}

async function testPerformance() {
  console.log('⚡ Performance Test\n')
  
  const testData = {
    ticketCode: 'PERF123TEST',
    eventId: 1,
    eventScheduleId: '2024-01-15'
  }

  const iterations = 10
  const times = []

  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now()
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/checkin-app/checkin/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      })
      
      await response.json()
      const endTime = Date.now()
      times.push(endTime - startTime)
      
    } catch (error) {
      console.log(`   Request ${i + 1} failed: ${error.message}`)
    }
  }

  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length
    const minTime = Math.min(...times)
    const maxTime = Math.max(...times)
    
    console.log(`📊 Performance Results (${iterations} requests):`)
    console.log(`   Average: ${avgTime.toFixed(2)}ms`)
    console.log(`   Min: ${minTime}ms`)
    console.log(`   Max: ${maxTime}ms`)
    console.log(`   Target: <100ms`)
    
    if (avgTime < 100) {
      console.log('   ✅ Performance target met\n')
    } else {
      console.log('   ⚠️  Performance target not met\n')
    }
  }
}

async function main() {
  console.log('🚀 Enhanced Checkin Scan API Test Suite')
  console.log('=' .repeat(50))
  
  await testCheckinScanAPI()
  await testPerformance()
  
  console.log('📝 Notes:')
  console.log('- These tests require a running server with test data')
  console.log('- Authentication tests will fail without proper admin tokens')
  console.log('- Performance results depend on database and network conditions')
  console.log('- For production testing, use proper test data and authentication')
}

// Run the tests
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testCheckinScanAPI, testPerformance }
