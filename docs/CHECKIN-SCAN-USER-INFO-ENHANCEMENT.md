# Checkin Scan API - User Information & Event Date Enhancement

## 🎯 Overview

Enhanced the checkin scan API to include comprehensive user information and event date from the events_schedules table for better UI display.

## ✅ New Features Added

### 📅 Event Date Integration
- **LEFT JOIN with events_schedules**: Added join to fetch event date from `events_schedules` table
- **Event Date Field**: New `eventDate` field in response containing the actual event date/time
- **Proper Relationship**: Joins using `t.event_schedule_id = es.id AND es._parent_id = t.event_id`

### 👤 Enhanced User Information
- **Complete User Details**: Now returns all user information including:
  - `email` - User's email address
  - `phoneNumber` - User's phone number  
  - `firstName` - User's first name
  - `lastName` - User's last name
  - `id` - User ID for reference

## 🔧 Technical Implementation

### Database Query Enhancement

```sql
SELECT
  t.id, t.ticket_code, t.attendee_name, t.seat, t.ticket_price_info,
  t.event_id, t.event_schedule_id, t.status, t.user_id,
  u.email, u.phone_number, u.first_name, u.last_name,  -- Enhanced user info
  t.order_id, o.status as order_status,
  CASE WHEN cr.id IS NOT NULL THEN true ELSE false END as is_checked_in,
  cr.check_in_time, cr.checked_in_by_id, cr.ticket_given_time, cr.ticket_given_by,
  a.email as checked_in_by_email,
  COALESCE(el.title, e.title) as event_title,
  es.date as event_date                                 -- New event date field
FROM tickets t
LEFT JOIN users u ON t.user_id = u.id
LEFT JOIN orders o ON t.order_id = o.id
LEFT JOIN checkin_records cr ON cr.ticket_code = t.ticket_code 
  AND cr.event_id = t.event_id AND cr.deleted_at IS NULL
LEFT JOIN admins a ON cr.checked_in_by_id = a.id
LEFT JOIN events e ON t.event_id = e.id
LEFT JOIN events_locales el ON e.id = el._parent_id AND el._locale = 'en'
LEFT JOIN events_schedules es ON t.event_schedule_id = es.id AND es._parent_id = t.event_id  -- New JOIN
WHERE t.ticket_code = ? 
ORDER BY t.created_at DESC
LIMIT 1
```

### Response Format Enhancement

**Before:**
```json
{
  "user": {
    "id": 456,
    "email": "<EMAIL>"
  }
}
```

**After:**
```json
{
  "user": {
    "id": 456,
    "email": "<EMAIL>",
    "phoneNumber": "+84123456789",
    "firstName": "John",
    "lastName": "Doe"
  },
  "eventDate": "2024-01-15T19:00:00.000Z"
}
```

## 📊 Database Schema Understanding

### events_schedules Table Structure
```sql
CREATE TABLE "events_schedules" (
  "_order" integer NOT NULL,
  "_parent_id" integer NOT NULL,        -- References events.id
  "id" varchar PRIMARY KEY NOT NULL,    -- Referenced by tickets.event_schedule_id
  "date" timestamp(3) with time zone    -- The actual event date/time
);
```

### Relationship Flow
```
tickets.event_schedule_id → events_schedules.id
events_schedules._parent_id → events.id
tickets.event_id → events.id
```

## 🎨 UI Benefits

### Enhanced Display Information
With the new fields, the UI can now display:

1. **Complete User Profile**:
   - Full name: "John Doe" (firstName + lastName)
   - Contact info: "<EMAIL>" and "+84123456789"
   - Better user identification

2. **Accurate Event Timing**:
   - Exact event date/time from schedule
   - Better event information display
   - Improved user experience

### Example UI Display
```
✅ Ticket Valid for Check-in

Event: Concert 2024
Date: January 15, 2024 at 7:00 PM
Seat: A1

Attendee: John Doe
Email: <EMAIL>
Phone: +84123456789

Ticket Code: ABC123DEF
Status: Paid & Ready
```

## 📁 Files Modified

### Core API
- `src/app/(payload)/api/checkin-app/checkin/scan/route.ts`
  - Added `event_date` to `TicketValidationRecord` interface
  - Enhanced SQL query with `events_schedules` LEFT JOIN
  - Updated response to include `eventDate` and complete user info

### Documentation
- `docs/api/checkin-scan.md`
  - Updated response examples with new fields
  - Enhanced feature descriptions

### Testing
- `tests/functions/integration/api/checkin-app/checkin-scan.test.ts`
  - Added `event_date` to mock data
  - Updated test expectations to include new fields

## 🧪 Testing

### Test Data Enhancement
```javascript
const mockTicket = {
  // ... existing fields
  event_date: '2024-01-15T19:00:00.000Z',  // New field
  phone_number: '+84123456789',            // Enhanced user info
  first_name: 'John',                      // Enhanced user info
  last_name: 'Doe',                        // Enhanced user info
}
```

### Test Expectations
```javascript
expect(data.ticket).toMatchObject({
  eventDate: '2024-01-15T19:00:00.000Z',
  user: {
    phoneNumber: '+84123456789',
    firstName: 'John',
    lastName: 'Doe',
  }
})
```

## 🚀 Performance Impact

### Query Optimization
- **Additional JOIN**: Added 1 more LEFT JOIN (events_schedules)
- **Performance**: Minimal impact due to proper indexing
- **Efficiency**: Still maintains single-query approach
- **Indexes Used**:
  - `events_schedules_parent_id_idx` on `_parent_id`
  - Existing ticket indexes remain effective

### Response Size
- **Minimal Increase**: Only adds event date and user phone/names
- **Better Value**: Significantly improves UI capabilities
- **No Breaking Changes**: Maintains backward compatibility

## 🔮 Future Enhancements

1. **Event Location**: Add event location from events table
2. **Schedule Details**: Include time and description from events_schedules_details
3. **User Avatar**: Add user profile image if available
4. **Event Images**: Include event banner/thumbnail for UI
5. **Timezone Support**: Handle timezone conversion for event dates

## 📞 Usage Example

### Request
```bash
curl -X POST /api/checkin-app/checkin/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "ticketCode": "ABC123DEF",
    "eventId": 1,
    "eventScheduleId": "2024-01-15"
  }'
```

### Enhanced Response
```json
{
  "success": true,
  "message": "Ticket is valid for check-in",
  "ticket": {
    "id": 123,
    "ticketCode": "ABC123DEF",
    "attendeeName": "John Doe",
    "seat": "A1",
    "eventTitle": "Concert 2024",
    "eventDate": "2024-01-15T19:00:00.000Z",
    "status": "booked",
    "user": {
      "id": 456,
      "email": "<EMAIL>",
      "phoneNumber": "+84123456789",
      "firstName": "John",
      "lastName": "Doe"
    },
    "order": {
      "id": 789,
      "status": "completed"
    },
    "isCheckedIn": false
  }
}
```

---

**Enhancement Status**: ✅ Complete - Ready for UI integration with enhanced user information and event date display.
