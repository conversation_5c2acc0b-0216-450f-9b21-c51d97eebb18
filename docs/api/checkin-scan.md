# Check-in Scan API Documentation

## Overview

The Check-in Scan API validates ticket eligibility for check-in by performing comprehensive validation checks including ticket status, order completion, and existing check-in records. This API is optimized for performance using a single SQL query with JOINs to minimize database round trips.

## Endpoint

```
POST /api/checkin-app/checkin/scan
```

## Authentication

This endpoint requires admin authentication. Include the authorization header with a valid admin session token.

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer <admin-session-token>
```

### Request Body
```json
{
  "ticketCode": "string",     // Required: The ticket code to validate
  "eventId": "number",        // Required: The event ID
  "eventScheduleId": "string" // Optional: The event schedule ID
}
```

### Request Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `ticketCode` | string | Yes | The unique ticket code to validate |
| `eventId` | number | Yes | The ID of the event |
| `eventScheduleId` | string | No | The specific event schedule ID (if applicable) |

## Response Format

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Ticket is valid for check-in",
  "ticket": {
    "id": 123,
    "ticketCode": "ABC123DEF",
    "attendeeName": "John Doe",
    "seat": "A1",
    "ticketPriceInfo": {
      "name": "VIP",
      "price": 100000,
      "currency": "VND"
    },
    "eventId": 1,
    "eventScheduleId": "2024-01-15",
    "eventTitle": "Concert 2024",
    "eventDate": "2024-01-15T19:00:00.000Z",
    "status": "booked",
    "user": {
      "id": 456,
      "email": "<EMAIL>",
      "phoneNumber": "+84123456789",
      "firstName": "John",
      "lastName": "Doe"
    },
    "order": {
      "id": 789,
      "status": "completed"
    },
    "isCheckedIn": false,
    "checkinRecord": null
  }
}
```

### Error Response (400 Bad Request)

```json
{
  "success": false,
  "message": "Error message in user's language"
}
```

## Validation Logic

The API performs the following validations in order:

1. **Authentication Check**: Verifies admin user is authenticated
2. **Input Validation**: Ensures required fields are provided
3. **Ticket Existence**: Checks if ticket exists for the given parameters
4. **Ticket Status**: Validates ticket status is 'booked'
5. **Order Validation**: Ensures order exists and is 'completed'
6. **Check-in Status**: Verifies ticket hasn't been checked in already

## Error Codes

| Error Code | English Message | Vietnamese Message | Description |
|------------|-----------------|-------------------|-------------|
| `CHECKIN001` | Ticket not found | Vé không tồn tại | No ticket found matching the criteria |
| `CHECKIN005` | Unauthorized - Invalid admin user | Không có quyền truy cập - Vui lòng đăng nhập | Admin authentication failed |
| `CHECKIN010` | Ticket code is required | Mã vé không được để trống | Missing ticket code in request |
| `CHECKIN014` | Please choose event, it is required | Vui lòng chọn sự kiện, đây là thông tin bắt buộc | Missing event ID in request |
| `CHECKIN015` | Ticket is not paid or not valid for check-in | Vé chưa được thanh toán hoặc không hợp lệ để check-in | Ticket status is not 'booked' |
| `CHECKIN016` | No order found for this ticket | Không tìm thấy đơn hàng cho vé này | Ticket has no associated order |
| `CHECKIN017` | Order is not completed/paid | Đơn hàng chưa được thanh toán hoàn tất | Order status is not 'completed' |
| `CHECKIN018` | Ticket has already been checked in | Vé đã được check-in trước đó | Ticket already has a check-in record |

## Performance Optimizations

### Single Query Approach
The API uses a single SQL query with multiple JOINs to fetch all required data:
- Ticket information
- Complete user details (email, phone number, first name, last name)
- Order status
- Existing check-in records
- Admin information (for existing check-ins)
- Event details with localization support
- Event schedule date from events_schedules table

This approach reduces database round trips from potentially 6+ queries to just 1 query.

### Indexed Fields
The query leverages existing database indexes on:
- `tickets.ticket_code`
- `tickets.event_id`
- `tickets.event_schedule_id`
- `tickets.status`
- `checkin_records.ticket_code`

## Usage Examples

### Valid Ticket Check
```bash
curl -X POST /api/checkin-app/checkin/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "ticketCode": "ABC123DEF",
    "eventId": 1,
    "eventScheduleId": "2024-01-15"
  }'
```

### Response for Valid Ticket
```json
{
  "success": true,
  "message": "Ticket is valid for check-in",
  "ticket": {
    "id": 123,
    "ticketCode": "ABC123DEF",
    "attendeeName": "John Doe",
    "seat": "A1",
    "status": "booked",
    "isCheckedIn": false
  }
}
```

### Response for Invalid Ticket
```json
{
  "success": false,
  "message": "Ticket not found"
}
```

## Integration Notes

1. **Error Handling**: All errors return HTTP 400 with localized error messages
2. **Logging**: Errors are logged to console with full error details
3. **Security**: Uses parameterized queries to prevent SQL injection
4. **Internationalization**: Error messages are localized based on user's language preference

## Related APIs

- `POST /api/checkin-app/validate/{ticket-code}` - Alternative validation endpoint
- `POST /api/checkin-app/checkin/{ticket-code}` - Actual check-in endpoint
- `POST /api/checkin-app/customer-checkin` - Customer self check-in endpoint

## Testing

### Unit Tests
The API includes comprehensive unit tests covering:
- Authentication validation
- Input parameter validation
- Ticket existence checks
- Status validation logic
- Error handling scenarios

### Integration Tests
Integration tests verify:
- Database query performance
- End-to-end validation flow
- Error response formatting
- Localization functionality

### Performance Tests
Performance benchmarks ensure:
- Sub-100ms response times for valid tickets
- Efficient database query execution
- Minimal memory usage

## Changelog

### Version 2.0 (Current)
- **Performance**: Optimized to use single SQL query instead of multiple queries
- **Security**: Added parameterized queries to prevent SQL injection
- **Error Handling**: Comprehensive error codes with localization
- **Validation**: Enhanced validation logic for all ticket states
- **Documentation**: Complete API documentation with examples

### Version 1.0 (Legacy)
- Basic ticket validation using multiple PayloadCMS queries
- Limited error handling
- No performance optimizations

## Best Practices

1. **Always validate authentication** before processing requests
2. **Use parameterized queries** to prevent SQL injection
3. **Implement comprehensive error handling** with user-friendly messages
4. **Log errors** for debugging while protecting sensitive data
5. **Optimize database queries** to minimize round trips
6. **Provide clear API documentation** with examples
7. **Include performance benchmarks** in testing
