# Check-in System API Reference

## Quick Start

The check-in system provides APIs for validating and processing ticket check-ins at events. All APIs require admin authentication and follow consistent error handling patterns.

## Core APIs

### 1. Scan & Validate Ticket
**Endpoint**: `POST /api/checkin-app/checkin/scan`

**Purpose**: Validate if a ticket is eligible for check-in

**Key Features**:
- Single optimized SQL query for performance
- Comprehensive validation (ticket status, order completion, existing check-ins)
- Localized error messages
- SQL injection protection

**Quick Example**:
```bash
curl -X POST /api/checkin-app/checkin/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"ticketCode": "ABC123", "eventId": 1}'
```

### 2. Validate by Ticket Code
**Endpoint**: `POST /api/checkin-app/validate/{ticket-code}`

**Purpose**: Alternative validation endpoint with seat search capability

**Key Features**:
- Search by ticket code or seat label
- Handles multiple tickets per seat
- Returns detailed ticket information

### 3. Perform Check-in
**Endpoint**: `POST /api/checkin-app/checkin/{ticket-code}`

**Purpose**: Actually check-in a validated ticket

**Key Features**:
- Creates check-in record
- Updates ticket status
- Returns check-in confirmation

### 4. Customer Self Check-in
**Endpoint**: `POST /api/checkin-app/customer-checkin`

**Purpose**: Allow customers to check themselves in

**Key Features**:
- Email and ticket code validation
- Self-service check-in flow
- Sister ticket handling

## Validation Flow

```mermaid
graph TD
    A[Receive Request] --> B[Authenticate Admin]
    B --> C[Validate Input]
    C --> D[Query Ticket Data]
    D --> E{Ticket Exists?}
    E -->|No| F[Return CHECKIN001]
    E -->|Yes| G{Status = 'booked'?}
    G -->|No| H[Return CHECKIN015]
    G -->|Yes| I{Order Completed?}
    I -->|No| J[Return CHECKIN017]
    I -->|Yes| K{Already Checked In?}
    K -->|Yes| L[Return CHECKIN018]
    K -->|No| M[Return Valid Ticket]
```

## Error Handling

All APIs use consistent error handling:

```json
{
  "success": false,
  "message": "Localized error message"
}
```

### Common Error Codes
- `CHECKIN001`: Ticket not found
- `CHECKIN005`: Authentication failed
- `CHECKIN010`: Missing ticket code
- `CHECKIN014`: Missing event ID
- `CHECKIN015`: Ticket not booked
- `CHECKIN016`: No order found
- `CHECKIN017`: Order not completed
- `CHECKIN018`: Already checked in

## Performance Considerations

### Database Optimization
- **Single Query Approach**: The scan API uses one optimized query instead of multiple
- **Proper Indexing**: Leverages existing indexes on ticket_code, event_id, status
- **JOIN Strategy**: Uses LEFT JOINs to fetch related data efficiently

### Response Times
- **Target**: Sub-100ms for ticket validation
- **Monitoring**: Log slow queries for optimization
- **Caching**: Consider caching frequently accessed event data

## Security Features

### SQL Injection Prevention
```javascript
// ✅ Good - Parameterized query
const query = sql`SELECT * FROM tickets WHERE ticket_code = ${ticketCode}`

// ❌ Bad - String concatenation
const query = `SELECT * FROM tickets WHERE ticket_code = '${ticketCode}'`
```

### Authentication
- All endpoints require admin authentication
- Use PayloadCMS auth system
- Validate user permissions per event

### Input Validation
- Sanitize all input parameters
- Validate required fields
- Check parameter types and formats

## Testing Strategy

### Unit Tests
```javascript
describe('Checkin Scan API', () => {
  it('should validate ticket successfully', async () => {
    // Test valid ticket scenario
  })
  
  it('should reject invalid ticket', async () => {
    // Test error scenarios
  })
})
```

### Integration Tests
- Test complete validation flow
- Verify database interactions
- Check error response formats

### Performance Tests
- Measure response times
- Test with concurrent requests
- Monitor database query performance

## Development Guidelines

### Code Structure
```
src/app/(payload)/api/checkin-app/
├── checkin/
│   ├── scan/route.ts           # New optimized scan API
│   └── [ticket-code]/route.ts  # Actual check-in
├── validate/
│   └── [ticket-code]/route.ts  # Alternative validation
└── customer-checkin/route.ts   # Self check-in
```

### Best Practices
1. **Always authenticate** before processing
2. **Use parameterized queries** for security
3. **Handle errors gracefully** with user-friendly messages
4. **Log errors** for debugging
5. **Optimize database queries** for performance
6. **Write comprehensive tests**
7. **Document API changes**

### Error Message Localization
```javascript
// Add to src/config/error-code.ts
export const CHECKIN_ERROR_CODE = {
  CHECKIN019: 'New error message in Vietnamese',
}

// Add to src/payload-config/i18n/locales/en.json
{
  "errorCode": {
    "CHECKIN019": "New error message in English"
  }
}
```

## Monitoring & Debugging

### Logging
```javascript
console.error('Checkin scan validation error:', error)
```

### Metrics to Track
- API response times
- Error rates by error code
- Database query performance
- Authentication failures

### Common Issues
1. **Slow queries**: Check database indexes
2. **Authentication errors**: Verify admin session tokens
3. **Validation failures**: Check ticket and order statuses
4. **Localization issues**: Verify error code translations

## Migration Notes

### From Legacy API
The new scan API (`/checkin/scan`) replaces multiple database queries with a single optimized query:

**Before** (5+ queries):
1. Find ticket by ID
2. Validate ticket info
3. Check ticket status
4. Find and validate order
5. Check existing check-in records

**After** (1 query):
- Single JOIN query fetches all required data

### Breaking Changes
- Request format changed from individual parameters to structured JSON
- Response format includes more detailed ticket information
- Error codes standardized with localization support

## Support

For questions or issues:
1. Check the API documentation in `docs/api/checkin-scan.md`
2. Review test files for usage examples
3. Check error code definitions in `src/config/error-code.ts`
4. Consult the PayloadCMS documentation for auth patterns
